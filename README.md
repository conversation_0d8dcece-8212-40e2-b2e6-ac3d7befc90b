# awesome_cloudflare_workers

## worker-dl
Download file with cloudfalre pages.dev proxy.

Usage: rename `worker-dl.js` to `_worker.js` and upload to your pages.dev

## worker-vless

Deploy vless proxy server on cloudfalre pages.dev.

Usage: rename `worker-vless.js` to `_worker.js` and upload to your pages.dev

## worker-vless-sub

Deploy vless proxy server with subscribe link on cloudfalre pages.dev.

Usage: rename `worker-vless-sub.js` to `_worker.js` and upload to your pages.dev

## worker-vless-nat64

Deploy vless proxy serverAccess to cloudflare ip via nat64) on cloudfalre pages.dev.

Usage: rename `worker-vless-nat64.js` to `_worker.js` and upload to your pages.dev

## worker-proxy

Proxy your traffic to target host via pages.dev

Usage: rename `worker-proxy.js` to `_worker.js` and upload to your pages.dev
